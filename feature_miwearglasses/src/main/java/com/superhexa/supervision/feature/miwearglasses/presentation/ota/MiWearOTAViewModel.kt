package com.superhexa.supervision.feature.miwearglasses.presentation.ota

import android.content.Context
import android.os.Handler
import android.os.Looper
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import com.superhexa.lib.channel.model.DeviceModelManager
import com.superhexa.lib.channel.tools.BlueDeviceDbHelper
import com.superhexa.lib.channel.tools.ConnectUtil
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.DeviceState
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95Action
import com.superhexa.supervision.feature.channel.presentation.newversion.bean.o95.O95StateLiveData
import com.superhexa.supervision.feature.channel.presentation.newversion.bluetooth.command.o95.PreparingUpgrade
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.CameraJointDetectionManager
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.ota.MiWearDeviceOTAProgressHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.ota.MiWearOTAListener
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiConfigHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiConfigHandler.Companion.WIFI_AP
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiP2PConfigHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.miwear.proto.wifi.MiWearWiFiP2PConfigHandler.WIFI_P2P
import com.superhexa.supervision.feature.channel.presentation.newversion.business.ota.MiWearOtaActionHandler
import com.superhexa.supervision.feature.channel.presentation.newversion.business.utils.DecoratorUtil
import com.superhexa.supervision.feature.miwearglasses.R
import com.superhexa.supervision.feature.miwearglasses.presentation.media.handler.MiWearConnectP2POrAPHandler
import com.superhexa.supervision.feature.miwearglasses.presentation.ota.MiWearOTADownloadHelper.curProgress
import com.superhexa.supervision.feature.miwearglasses.presentation.space.dialogs.FileTransO95Callback
import com.superhexa.supervision.library.base.basecommon.commonbean.DeviceUpdateInfo
import com.superhexa.supervision.library.base.basecommon.commonbean.MiWearUpgradeInfo
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication.Companion.instance
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.tools.FileAndDirUtils
import com.superhexa.supervision.library.base.basecommon.tools.JsonUtils
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.net.retrofit.utils.NetWorkUtil
import com.superhexa.supervision.library.statistic.O95Statistic
import com.xiaomi.fitness.device.contact.export.IDeviceSyncCallback
import com.xiaomi.fitness.device.contact.export.SyncResult
import com.xiaomi.wear.protobuf.nano.SystemProtos
import com.xiaomi.wear.protobuf.nano.SystemProtos.WiFiAP
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File

/**
 * 类描述:
 * 创建日期: 2024/9/29 on 11:01
 * 作者: qintaiyuan
 */
@Suppress("TooManyFunctions")
class MiWearOTAViewModel : BaseMVIViewModel<MiWearOTAState, MiWearOTAEffect, MiWearOTAEvent>() {
    private val bondDevice get() = BlueDeviceDbHelper.getBondDevice()
    private val decorator by lazy { DecoratorUtil.getDecorator<O95StateLiveData>(bondDevice) }
    private val otaHandler by lazy { MiWearOtaActionHandler().bind(decorator.liveData) }
    private val miWearWiFiConfigHandler by lazy { MiWearWiFiConfigHandler().bindDecorator(decorator) }
    val deviceStateLiveData = decorator.liveData
    private val handler by lazy { Handler(Looper.getMainLooper()) }
    private var isReconnecting = false
    private var lastFirmVersion = ""
    private var alertOTaState: Int = 1 // 1-空闲 2-下载 3-安装
    private var enterStartTime = System.currentTimeMillis()
    val otaType = mutableStateOf(OTAType.WIFI)

    init {
        observeSomeLiveData()
    }

    override fun initUiState() = MiWearOTAState()

    private fun observeSomeLiveData() {
        decorator.liveData.asFlow().map { it.deviceState }.distinctUntilChanged().onEach { state ->
            if (state is DeviceState.Disconnected) {
                sendEvent(MiWearOTAEvent.OnDeviceDisConnected)
            } else if (state is DeviceState.ChannelSuccess) {
                sendEvent(MiWearOTAEvent.OnDeviceChannelSuccess)
            }
        }.launchIn(viewModelScope)

        // 新 ViewModel 初始化时收集异常流
        MiWearOTADownloadHelper.downloadException
            .onEach { ex ->
                // 这里是新 ViewModel 的上下文，可直接处理
                Timber.e("otaDownloadException errMsg:${ex.message}")
                MiWearOTADownloadHelper.reset("download exception")
                showUpgradeFail(
                    oldState = mState.value,
                    upgradeTitle = instance.getString(R.string.libs_glasses_download_fail),
                    getExceptionString(ex.message)
                )
            }
            .launchIn(viewModelScope) // 绑定当前 ViewModel 的生命周期
    }

    @Suppress("ComplexMethod")
    override fun reduce(oldState: MiWearOTAState, event: MiWearOTAEvent) {
        when (event) {
            is MiWearOTAEvent.InitData -> initData(oldState, event.otaState, event.call)
            is MiWearOTAEvent.SwitchLowBatteryState -> {
                setState(oldState.copy(showLowBattery = event.show,
                    deviceUpdateState = DeviceOTAState.Update))
            }

            is MiWearOTAEvent.SwitchTemperatureState -> {
                showTemperatureTip(oldState, HIGH_TEMPERATURE, event.show)
            }

            is MiWearOTAEvent.SwitchLowStorageState -> {
                setState(oldState.copy(showLowStorage = event.show,
                    deviceUpdateState = DeviceOTAState.Update))
            }

            is MiWearOTAEvent.SwitchDeviceNetErrorState -> {
                setState(oldState.copy(showDeviceNetErrorTips = event.show))
            }

            is MiWearOTAEvent.SwitchDeviceBusyState -> {
                setState(oldState.copy(showDeviceBusyTips = event.show,
                    deviceUpdateState = DeviceOTAState.Update))
            }

            is MiWearOTAEvent.SwitchUpgradeFailState -> {
                showUpgradeFail(oldState,
                    showUpgradeFail = event.show,
                    upgradeTitle = instance.getString(R.string.libs_glasses_upload_title),
                    failMsg = instance.getString(R.string.libs_glasses_ota_net_error)
                )
            }

            is MiWearOTAEvent.StartOta -> {
                startOTA(oldState)
            }
            is MiWearOTAEvent.CheckUpdateState -> {
                checkDeviceUpdateState(oldState, event.call)
            }

            is MiWearOTAEvent.OnDeviceDisConnected -> dealDeviceDisConnectedAction(oldState)
            is MiWearOTAEvent.OnDeviceChannelSuccess -> dealDeviceChannelSuccessAction(oldState)
            is MiWearOTAEvent.OnExit -> {
                cleanHandMessage()
                viewModelScope.launch { closeWifiP2P() }
                setState(
                    oldState.copy(
                        showDeviceBusyTips = false,
                        showLowBattery = false,
                        showLowStorage = false,
                        showDeviceNetErrorTips = false
                    )
                )
            }
            is MiWearOTAEvent.UploadState -> {
                when (event.uploadState) {
                    is MiWearOTAUploadCallback.Uploading -> {
                        switchOTAState(oldState, DeviceOTAState.Uploading(event.uploadState.progress))
                    }
                    is MiWearOTAUploadCallback.UploadedComplete -> {
                        val uploadResult = event.uploadState.status
                        when (uploadResult) {
                            0 -> {
                                // upload success, 会等待固件给发送消息就是进度，参考 addOTAProgressListener
                                switchOTAState(oldState, DeviceOTAState.Installing(0))
                            }
                            1 -> {
                                // 上传失败，如果wifi不可用则弹出开启WIFI的弹窗
                                if (!NetWorkUtil.isWifiEnabled(instance)) {
                                    switchOTAState(
                                        oldState,
                                        DeviceOTAState.WIFIDialog(true)
                                    )
                                } else {
                                    // upload fail
                                    showUpgradeFail(
                                        oldState,
                                        upgradeTitle = instance.getString(R.string.libs_glasses_upload_title),
                                        failMsg = instance.getString(R.string.libs_glasses_ota_net_reason_error)
                                    )
                                }
                            }
                        }
                    }
                }
            }

            is MiWearOTAEvent.Reset -> {
                switchOTAState(oldState, DeviceOTAState.Update)
            }
        }
    }

    private fun dealDeviceDisConnectedAction(oldState: MiWearOTAState) {
        val deviceOTAState = oldState.deviceUpdateState
        Timber.tag(TAG).d("device-disconnected--otaState= $deviceOTAState")
        when (deviceOTAState) {
            is DeviceOTAState.Restarting -> dealDeviceRestartAction(oldState)
            is DeviceOTAState.Downloading,
            is DeviceOTAState.Installing,
            is DeviceOTAState.Uploading,
            is DeviceOTAState.PreparingUpdate -> {
                cleanHandMessage()
                switchOTAState(oldState, DeviceOTAState.Update)
                sendEffect(MiWearOTAEffect.DeviceDisconnected)
            }

            else -> {
            }
        }
    }

    @Suppress("MagicNumber")
    private fun dealDeviceChannelSuccessAction(oldState: MiWearOTAState) {
        val deviceOTAState = oldState.deviceUpdateState
        Timber.tag(TAG).d("device-channelSuccess--otaState= $deviceOTAState")

        var otaError = ""
        when {
            deviceOTAState is DeviceOTAState.Restarting && isReconnecting -> {
                cleanHandMessage()
                isReconnecting = false
                val curVersionCode = decorator.liveData.value?.deviceInfo?.firmwareVersion ?: ""
                Timber.tag(TAG).d("升级版本号=$lastFirmVersion, 升级后版本号=$curVersionCode")
                when {
                    // 1-空闲 2-下载 3-安装
                    alertOTaState != 2 && alertOTaState != 3 && lastFirmVersion != curVersionCode -> { // 升级后版本不一致，升级失败
                        otaError = "升级异常"
                        onDeviceUpdateFailed(oldState)
                    }

                    else -> { // 无待升级版本，提示升级成功
                        decorator.liveData.dispatchAction(O95Action.SyncUpdateState(null))
                        switchOTAState(oldState, DeviceOTAState.OTASuccess)
                    }
                }
            }

            else -> {}
        }
        val pair = O95Statistic.eventGlassesOTAPair()
        O95Statistic.eventV3(
            pair.first,
            pair.second,
            hasTypeStr = true,
            typeStr = when (deviceOTAState) {
                is DeviceOTAState.OTASuccess -> {
                    "success"
                }

                is DeviceOTAState.OTAFailed -> {
                    "failed"
                }

                else -> {
                    ""
                }
            },
            hasErrorCode = true,
            errorCode = otaError,
            hasDurationSec = true,
            durationSec = enterStartTime
        )
    }

    private fun dealDeviceRestartAction(oldState: MiWearOTAState) {
        viewModelScope.launch {
            val device = bondDevice
            if (device != null && !isReconnecting) {
                Timber.tag(TAG).d("6.开始重连设备")
                isReconnecting = true
                lastFirmVersion = decorator.liveData.value?.updateInfo?.versionCode ?: ""
                reConnectDevice()
                handler.postDelayed(
                    {
                        Timber.tag(TAG).d("7.连接设备失败 超时时间=$DEVICE_RESTART_TIMEOUT")
                        onDeviceUpdateFailed(oldState)
                        isReconnecting = false
                    },
                    DEVICE_RESTART_TIMEOUT
                )
            }
        }
    }

    private fun reConnectDevice() = viewModelScope.launch {
        repeat(MAX_RETRY_COUNT) {
            val channelSuccess = decorator.isChannelSuccess()
            Timber.tag(TAG).d("6.重连设备--channelSuccess=$channelSuccess")
            // 执行你的任务
            if (channelSuccess) {
                return@launch
            } else {
                bondDevice?.let {
                    decorator.reConnect(it)
                }
            }
            delay(RETRY_DELAY_TIME)
        }
    }

    private fun onDeviceUploadingError(oldState: MiWearOTAState) {
        showUpgradeFail(
            oldState,
            upgradeTitle = instance.getString(R.string.libs_glasses_upload_title),
            instance.getString(R.string.libs_glasses_ota_net_error)
        )
    }

    private fun onDeviceNetWorkError(oldState: MiWearOTAState) {
        val state = oldState.copy(showDeviceNetErrorTips = true)
        switchOTAState(state, DeviceOTAState.Update)
    }

    private fun onDeviceUpdateFailed(oldState: MiWearOTAState) {
        instance.toast(R.string.libs_device_update_failed)
        switchOTAState(oldState, DeviceOTAState.Update)
    }

    @Suppress("MagicNumber")
    private fun initData(oldState: MiWearOTAState, otaState: Int, call: () -> Unit) =
        viewModelScope.launch {
            alertOTaState = otaState
            val updateInfo = deviceStateLiveData.value?.updateInfo
            Timber.tag(TAG).d("otaState=$otaState, updateInfo=$updateInfo")
            if (otaState == 2 || otaState == 3) { // 2-下载 3-安装
                initAlertOtaState(oldState)
            } else if (updateInfo != null) {
                updateUpdateDesc(oldState, updateInfo)
            } else {
                // 请求获取当前版本更新日志
                val info = fetchDeviceUpdateInfo()
                Timber.tag(TAG).d("default  updateInfo=$info")
                updateUpdateDesc(oldState, info)
            }

            call.invoke()
        }

    private suspend fun initAlertOtaState(oldState: MiWearOTAState) {
        setState(
            oldState.copy(
                updateDesc = "",
                deviceVersion = "",
                deviceUpdateState = DeviceOTAState.PreparingUpdate
            )
        )
        addOTAProgressListener(oldState)
        // 请求获取当前版本更新日志
        val info = fetchDeviceUpdateInfo(false)
        Timber.tag(TAG).d("alert_ota  updateInfo=$info")
        if (info != null) {
            val copy = mState.value.copy(
                deviceVersion = info.versionCode,
                updateDesc = info.versionDesc
            )
            setState(copy)
            addOTAProgressListener(copy)
        }
    }

    private fun updateUpdateDesc(oldState: MiWearOTAState, updateInfo: MiWearUpgradeInfo?) {
        val newState = if (updateInfo != null) {
            oldState.copy(
                deviceVersion = instance.getString(R.string.deviceVersion)
                    .format(
                        updateInfo.versionCode,
                        updateInfo.getNormalFileSize() / CONVERSION_CONSTANT
                    ),
                updateDesc = updateInfo.versionDesc,
                deviceUpdateState = DeviceOTAState.Update
            )
        } else {
            val curVersionCode = decorator.liveData.value?.deviceInfo?.firmwareVersion ?: ""
            oldState.copy(
                deviceVersion = if (curVersionCode.isNotNullOrEmpty()) "V$curVersionCode" else "",
                updateDesc = "",
                deviceUpdateState = DeviceOTAState.LatestVersion
            )
        }
        setState(newState)
        addOTAProgressListener(newState)
    }

    private fun checkDeviceUpdateState(
        oldState: MiWearOTAState,
        call: (MiWearUpgradeInfo?) -> Unit
    ) = viewModelScope.launch {
        if (NetWorkUtil.isNetWorkValidated(instance)) {
            val info = fetchDeviceUpdateInfo()
            updateUpdateDesc(oldState, info)
            call.invoke(info)
        } else {
            call.invoke(null)
        }
    }

    private suspend fun fetchDeviceUpdateInfo(autoSyncUpdateInfo: Boolean = true): MiWearUpgradeInfo? {
        return bondDevice?.let {
            otaHandler.getDeviceUpdateInfo(it.sn ?: "", it.miWearModel, autoSyncUpdateInfo)
        }
    }

    private fun addOTAProgressListener(oldState: MiWearOTAState) {
        Timber.d("addOTAProgressListener called $oldState")
        val pair = O95Statistic.eventGlassesOTAPair()
        MiWearDeviceOTAProgressHandler.addCallback(object : MiWearOTAListener {
            override fun onPrepare(did: String) {
                Timber.tag(TAG).d("0.开始准备")
                cleanHandMessage()
                switchOTAState(oldState, DeviceOTAState.PreparingUpdate)
            }

            override fun onDownLoading(did: String, percent: Float) {
                Timber.tag(TAG).d("1.文件下载中percent:$percent")
                cleanHandMessage()
                switchOTAState(oldState, DeviceOTAState.Downloading(progress = percent.toInt()))
            }

            override fun onInstalling(did: String, percent: Float) {
                Timber.tag(TAG).d("2.下载完成/安装中percent:$percent")
                cleanHandMessage()
                switchOTAState(oldState, DeviceOTAState.Installing(progress = percent.toInt()))
            }

            override fun onFailed(did: String) {
                Timber.tag(TAG).d("3.下载失败/传输中断, otaType:$otaType")
                cleanHandMessage()
                onDeviceNetWorkError(oldState)

                O95Statistic.eventV3(
                    pair.first,
                    pair.second,
                    hasTypeStr = true,
                    typeStr = "failed",
                    hasErrorCode = true,
                    errorCode = "下载失败/传输中断",
                    hasDurationSec = true,
                    durationSec = enterStartTime
                )
            }

            override fun onInStallFailed(did: String) {
                Timber.tag(TAG).d("4.安装失败")
                cleanHandMessage()
                onDeviceUpdateFailed(oldState)

                O95Statistic.eventV3(
                    pair.first,
                    pair.second,
                    hasTypeStr = true,
                    typeStr = "failed",
                    hasErrorCode = true,
                    errorCode = "安装失败",
                    hasDurationSec = true,
                    durationSec = enterStartTime
                )
            }

            override fun onRestart(did: String) {
                cleanHandMessage()
                Timber.tag(TAG).d("5.安装完成/即将重启")
                launch {
                    closeWifiP2P()
                    MiWearOTAWorkManager.cancelUpload(true)
                }
                switchOTAState(oldState, DeviceOTAState.Restarting)

                O95Statistic.eventV3(
                    pair.first,
                    pair.second,
                    hasTypeStr = true,
                    typeStr = "success",
                    hasDurationSec = true,
                    durationSec = enterStartTime
                )
            }

            override fun onLowBattery(did: String, minBattery: Int) {
                cleanHandMessage()
                Timber.tag(TAG).d("6-低电（不启动安装）")
                val state = oldState.copy(deviceUpdateState = DeviceOTAState.Update)
                showLowBatteryTip(state, minBattery)

                O95Statistic.eventV3(
                    pair.first,
                    pair.second,
                    hasTypeStr = true,
                    typeStr = "failed",
                    hasErrorCode = true,
                    errorCode = "低电（不启动安装）",
                    hasDurationSec = true,
                    durationSec = enterStartTime
                )
            }

            override fun onTemperatureError(did: String, code: Int) {
                cleanHandMessage()
                Timber.tag(TAG).d("6-低电（不启动安装）")
                val state = oldState.copy(deviceUpdateState = DeviceOTAState.Update)
                showTemperatureTip(state, code, true)

                O95Statistic.eventV3(
                    pair.first,
                    pair.second,
                    hasTypeStr = true,
                    typeStr = "failed",
                    hasErrorCode = true,
                    errorCode = "低温（不启动安装）",
                    hasDurationSec = true,
                    durationSec = enterStartTime
                )
            }
        })
    }

    fun isChannelSuccess() = decorator.isChannelSuccess()

    suspend fun getWifiConfig(): SystemProtos.WiFiConfig.List? {
        return miWearWiFiConfigHandler.getWiFiConfig()
    }

    private fun startOTA(oldState: MiWearOTAState) = viewModelScope.launch {
        Timber.tag(TAG).d("startOTA")
        switchOTAState(oldState, DeviceOTAState.PreparingUpdate)
        val updateInfo = deviceStateLiveData.value?.updateInfo
        if (updateInfo != null) {
            decorator.sendMiWearCommand(
                PreparingUpgrade(updateInfo, if (otaType.value == OTAType.WIFI) 0 else 1),
                object : IDeviceSyncCallback.Stub() {
                    override fun onSyncSuccess(did: String?, type: Int, result: SyncResult?) {
                        val prepareOtaResponse = result?.packet?.system?.prepareOtaResponse
                        Timber.tag(TAG)
                            .d("startOTA----otaType:$otaType, prepareOtaResponse=${prepareOtaResponse?.prepareStatus}")
                        when (prepareOtaResponse?.prepareStatus) {
                            READY -> {
                                prepareDownload(oldState)
                            }

                            LOW_BATTERY -> {
                                val battery = prepareOtaResponse.minBattery
                                showLowBatteryTip(
                                    oldState.copy(deviceUpdateState = DeviceOTAState.Update),
                                    battery
                                )
                            }

                            LOW_STORAGE -> {
                                showLowStorageTip(oldState.copy(deviceUpdateState = DeviceOTAState.Update))
                            }

                            NETWORK_ERROR -> {
                                instance.toast(R.string.libs_ota_device_net_error)
                                switchOTAState(oldState, DeviceOTAState.Update)
                            }

                            BUSY -> {
                                instance.toast(R.string.libs_ota_device_basy)
                                switchOTAState(oldState, DeviceOTAState.Update)
                            }

                            HIGH_TEMPERATURE,
                            LOW_TEMPERATURE -> {
                                showTemperatureTip(oldState, prepareOtaResponse.prepareStatus, true)
                            }

                            else -> {
                                instance.toast(R.string.libs_ota_device_error)
                                switchOTAState(oldState, DeviceOTAState.Update)
                            }
                        }
                    }

                    override fun onSyncError(did: String?, type: Int, code: Int) {
                        onDeviceUpdateFailed(oldState)
                        Timber.tag(TAG).d("startOTA失败----type=$type,code=$code")
                    }
                }
            )
        } else {
            switchOTAState(oldState, DeviceOTAState.Update)
        }
    }

    private fun prepareDownload(oldState: MiWearOTAState) {
        viewModelScope.launch { closeWifiP2P() }
        if (otaType.value == OTAType.WIFI) {
            switchToPrepareOTA(oldState)
        } else {
            if (!NetWorkUtil.isNetWorkValidated(instance)) {
                Timber.d("prepareDownload没有网络，显示错误弹窗")
                showUpgradeFail(
                    oldState,
                    upgradeTitle = instance.getString(R.string.libs_glasses_download_fail),
                    failMsg = instance.getString(R.string.libs_glasses_ota_net_error)
                )
            } else {
                Timber.d("prepareDownload有网络，准备下载")
                download(oldState)
            }
        }
    }

    private fun download(oldState: MiWearOTAState) {
        val updateInfo = deviceStateLiveData.value?.updateInfo
        Timber.tag(TAG).d("download frameWork:${bondDevice?.glassesFrameKey} updateInfo:$updateInfo")
        updateInfo?.let {
            // 下载资源包
            val updateDeviceInfo = it.convert(bondDevice?.model?.toInt() ?: 0)
            if (MiWearOTADownloadHelper.isSameMd5(updateDeviceInfo.checksum, updateDeviceInfo)) {
                Timber.i("MiWearOTADownloadHelper download but same md5直接upload")
                val path = MiWearOTADownloadHelper.getUpdatePatchFilePath(updateDeviceInfo)
                // 同文件，则不需要下载
                downloadComplete(it, oldState, path)
            } else {
                Timber.d("download prepare download url:${updateDeviceInfo.url}")
                if (prepareDownloadChecking(oldState, updateDeviceInfo)) {
                    val isDownloading =
                        MiWearOTADownloadHelper.isDownloading(updateDeviceInfo.url!!)
                    MiWearOTADownloadHelper.setDownloadingCallback { progress, path ->
                        Timber.d("downloading progress:$progress, isDownloading:$isDownloading")
                        switchOTAState(oldState, DeviceOTAState.Downloading(progress))
                        if (path.isNotEmpty()) {
                            downloadComplete(it, oldState, path)
                        }
                    }
                    // 下载资源包
                    if (isDownloading) {
                        Timber.d("MiWearOTADownloadHelper 当前文件下载中，无需重复下载 $curProgress")
                        switchOTAState(oldState, DeviceOTAState.Downloading(curProgress))
                        return
                    }
                    MiWearOTADownloadHelper.startDownload(updateDeviceInfo)
                }
            }
        }
    }

    /**
     * 下载文件前，条件判断
     */
    private fun prepareDownloadChecking(oldState: MiWearOTAState, updateDeviceInfo: DeviceUpdateInfo): Boolean {
        // 检查手机剩余空间
        val fileLength = (updateDeviceInfo.sizeByte ?: 0) / FileAndDirUtils.toGB
        val leftLength = FileAndDirUtils.leftAvaiableSpace(instance)
        Timber.d(" otaSize=%s, fileSpace=%s", fileLength, leftLength)
        var failTitle = ""
        var failDesc = ""
        if (leftLength < fileLength) {
            Timber.d(" otaDownloadError %s", "空间不足")
            failTitle = instance.getString(R.string.libs_glasses_download_fail)
            failDesc = instance.getString(R.string.libs_glasses_ota_space_error)
        } else if (!NetWorkUtil.isNetWorkAvaiable(instance)) {
            Timber.d(" otaDownloadError %s", "网络异常")
            failTitle = instance.getString(R.string.libs_glasses_download_fail)
            failDesc = instance.getString(R.string.libs_glasses_ota_net_error)
        } else if (updateDeviceInfo.url.isNullOrEmpty()) {
            failTitle = instance.getString(R.string.libs_glasses_download_fail)
            failDesc = instance.getString(R.string.libs_glasses_download_url_empty)
        }

        if (failTitle.isNotEmpty() && failDesc.isNotEmpty()) {
            showUpgradeFail(
                oldState,
                upgradeTitle = failTitle,
                failMsg = failDesc
            )
            return false
        }
        return true
    }

    private fun downloadComplete(updateInfo: MiWearUpgradeInfo, oldState: MiWearOTAState, downloadFilePath: String) {
        // 下载完成校验下md5
        MiWearOTADownloadHelper.reset("download complete")
        if (otaType.value == OTAType.NET) {
            val md5 = FileAndDirUtils.computeDigestValue(File(downloadFilePath), "md5")
            Timber.d("二、校验MD5 md5:$md5, checksum:${updateInfo.checksum}")
            if (updateInfo.checksum == md5) {
                Timber.d("三、下载完成下一步链接p2p ")
                CameraJointDetectionManager.checkIsChannelSuccess {
                    CameraJointDetectionManager.checkIsJointStatus { status ->
                        if (status && NetWorkUtil.isWifiEnabled(instance)) {
                            // 相机协同要弹错误的弹窗
                            showUpgradeFail(oldState,
                                upgradeTitle = instance.getString(R.string.libs_glasses_upload_title),
                                failMsg = instance.getString(R.string.libs_glasses_ota_net_reason_error)
                            )
                        } else {
                            switchOTAState(
                                oldState,
                                DeviceOTAState.Downloaded(downloadFilePath, updateInfo.checksum)
                            )
                        }
                    }
                }

            } else {
                Timber.e("三、校验md5值不一致")
            }
        }
    }

    private fun getExceptionString(exception: String?): String {
        return if (exception != null && exception.isNotNullOrEmpty()) {
            if (exception.contains("UnknownHostException") ||
                exception.contains("SocketException")
            ) {
                instance.getString(R.string.libs_glasses_ota_net_error)
            } else {
                exception
            }
        } else {
            exception ?: instance.getString(R.string.libs_glasses_download_error)
        }
    }

    fun startConnectP2P(fragment: Fragment, callback: (FileTransO95Callback) -> Unit) {
        Timber.tag(TAG).e("startConnectP2P")
        MiWearConnectP2POrAPHandler(fragment) { state ->
            Timber.tag(TAG).d("startConnectP2P---callback=$callback")
            callback.invoke(state)
        }.connectP2POrAP()
    }

    fun startUpload(context: Context, filePath: String, md5: String) {
        val ipAddress = MMKVUtils.decodeString(WIFI_P2P)
        Timber.d("WillTans---ipAddress=$ipAddress")
        val hostIp = if (DeviceModelManager.isEnableWiFiP2P() && ipAddress.isNotNullOrEmpty()) {
            ipAddress ?: ""
        } else {
            MiWearWiFiP2PConfigHandler.bindProcessToNetwork()
            val wifiAPJson = MMKVUtils.decodeString(WIFI_AP)
            val wiFiAP = JsonUtils.fromJson<WiFiAP>(wifiAPJson ?: "")
            Timber.i("wiFi AP:$wiFiAP")
            wiFiAP?.let {
                if (ConnectUtil.isConnectedTo(instance, it.ssid)) {
                    it.gateway
                } else {
                    Timber.tag(TAG).i("unConnectedTo wiFi AP:$wiFiAP")
                    ""
                }
            }
        }
        Timber.i("wiFi hostIp:$hostIp")
        hostIp?.let {
            MiWearOTAWorkManager.startUpload(context, hostIp, filePath, md5) { uploadState ->
                sendEvent(MiWearOTAEvent.UploadState(uploadState))
            }
        }
    }

    /**
     * 点击升级的时候先关闭
     */
    suspend fun closeWifiP2P() {
        Timber.d("closeWifiP2P called")
        val ipAddress = MMKVUtils.decodeString(WIFI_P2P)
        if (DeviceModelManager.isEnableWiFiP2P() && ipAddress.isNotNullOrEmpty()) {
            MiWearWiFiP2PConfigHandler.removeGroupIfNeed()
        } else {
            MiWearWiFiConfigHandler().bindDecorator(decorator)
                .tryDisconnectWifiAp()
        }
    }

    private fun switchOTAState(oldState: MiWearOTAState, deviceOTAState: DeviceOTAState) {
        setState(oldState.copy(deviceUpdateState = deviceOTAState))
    }

    private fun switchToPrepareOTA(oldState: MiWearOTAState) = viewModelScope.launch {
        cleanHandMessage()
        handler.postDelayed(
            {
                Timber.tag(TAG).d("升级失败，$TWENTY_SECOND 内无升级状态回调")
                onDeviceUpdateFailed(oldState)
            },
            TWENTY_SECOND
        )
        setState(oldState.copy(deviceUpdateState = DeviceOTAState.PreparingUpdate))
        addOTAProgressListener(oldState)
    }

    private fun cleanHandMessage() {
        handler.removeCallbacksAndMessages(null)
    }

    private fun showDeviceBusyTip(oldState: MiWearOTAState) = viewModelScope.launch {
        setState(oldState.copy(showDeviceBusyTips = true))
    }

    private fun showLowBatteryTip(oldState: MiWearOTAState, minBattery: Int) =
        viewModelScope.launch {
            Timber.tag(TAG).d("showLowBatteryTip---minBattery:$minBattery")
            val battery = if (minBattery <= 0) MIN_BATTERY else minBattery
            setState(
                oldState.copy(
                    minBatteryTip = instance.getString(R.string.libs_glasses_low_battery_desc)
                        .format("$battery%"),
                    showLowBattery = true
                )
            )
        }

    private fun showTemperatureTip(oldState: MiWearOTAState, code: Int, showTemperatureDlg: Boolean) =
        viewModelScope.launch {
            Timber.tag(TAG).d("showTemperatureTip---temperature called")
            setState(
                oldState.copy(
                    temperatureTitle = if (code == HIGH_TEMPERATURE) {
                        instance.getString(R.string.tip_feedback_high_temperature)
                    } else {
                        instance.getString(R.string.tip_feedback_low_temperature)
                    },
                    temperatureTip = if (code == HIGH_TEMPERATURE) {
                        instance.getString(R.string.tip_feedback_high_temperature_desc)
                    } else {
                        instance.getString(R.string.tip_feedback_low_temperature_desc)
                    },
                    showTemperatureDlg = showTemperatureDlg,
                    deviceUpdateState = DeviceOTAState.Update
                )
            )
        }

    private fun showLowStorageTip(oldState: MiWearOTAState) = viewModelScope.launch {
        setState(oldState.copy(showLowStorage = true))
    }

    private fun showUpgradeFail(
        oldState: MiWearOTAState,
        upgradeTitle: String,
        failMsg: String,
        showUpgradeFail: Boolean = true) = viewModelScope.launch {
        val state = oldState.copy(
            showUpgradeFail = showUpgradeFail,
            upgradeFailTitle = upgradeTitle,
            upgradeFailTip = failMsg,
            deviceUpdateState = DeviceOTAState.Update // 确保状态重置
        )
        Timber.d("showUpgradeFail called oldState:$oldState,\n newState:$state")
        setState(state)
    }

    override fun onCleared() {
        MiWearDeviceOTAProgressHandler.removeCallback()
        MiWearOTADownloadHelper.removeDownloadingCallback()
        cleanHandMessage()
        miWearWiFiConfigHandler.releaseDecorator()
        super.onCleared()
    }

    companion object {
        private const val READY = 0x00
        private const val BUSY = 0x01
        private const val DUPLICATED = 0x02
        private const val LOW_STORAGE = 0x03
        private const val LOW_BATTERY = 0x04
        private const val DOWNGRADE = 0x05
        private const val OP_NOT_SUPPORT = 0x06 // 指令操作/文件格式不支持
        private const val EXCEED_QUANTITY_LIMIT = 0x07 // 超过数量上限
        private const val NETWORK_ERROR = 0x08 // 设备网络错误
        private const val HIGH_TEMPERATURE = 0x09 // 设备高温
        private const val LOW_TEMPERATURE = 0x0A // 设备低温
        private const val FAILED = 0xFF
        private const val TAG = "MiWearOTAViewModel_TAG"
        private const val MAX_RETRY_COUNT = 9
        private const val RETRY_DELAY_TIME = 20_000L
        private const val DEVICE_RESTART_TIMEOUT = 200_000L
        private const val TWENTY_SECOND = 20_000L
        private const val MIN_BATTERY = 20
        private const val CONVERSION_CONSTANT = 1_024.0 * 1_024.0
    }
}
